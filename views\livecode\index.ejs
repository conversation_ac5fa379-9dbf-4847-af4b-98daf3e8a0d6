<%- include('../layout', {
    title: '活码管理',
    currentPage: 'livecodes',
    pageHeader: {
        title: '活码管理',
        subtitle: '管理您的所有活码链接',
        actions: [
            {
                text: '创建活码',
                url: '/livecode/create',
                class: 'btn-primary',
                icon: 'plus-circle'
            },
            {
                text: '批量导入',
                url: '/livecode/import',
                class: 'btn-outline-secondary',
                icon: 'upload'
            }
        ]
    },
    breadcrumb: [
        { name: '首页', url: '/admin/dashboard' },
        { name: '活码管理' }
    ]
}) %>

<% contentFor('body') %>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="搜索标题、代码或目标URL" value="<%= query.search || '' %>">
                </div>
            </div>
            
            <div class="col-md-2">
                <label for="status" class="form-label">状态</label>
                <select class="form-select" id="status" name="status">
                    <option value="">全部状态</option>
                    <option value="active" <%= query.status === 'active' ? 'selected' : '' %>>启用</option>
                    <option value="inactive" <%= query.status === 'inactive' ? 'selected' : '' %>>禁用</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="category" class="form-label">分类</label>
                <select class="form-select" id="category" name="category">
                    <option value="">全部分类</option>
                    <option value="promotion" <%= query.category === 'promotion' ? 'selected' : '' %>>推广</option>
                    <option value="product" <%= query.category === 'product' ? 'selected' : '' %>>产品</option>
                    <option value="service" <%= query.category === 'service' ? 'selected' : '' %>>服务</option>
                    <option value="other" <%= query.category === 'other' ? 'selected' : '' %>>其他</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="sortBy" class="form-label">排序</label>
                <select class="form-select" id="sortBy" name="sortBy">
                    <option value="created_at" <%= (query.sortBy || 'created_at') === 'created_at' ? 'selected' : '' %>>创建时间</option>
                    <option value="updated_at" <%= query.sortBy === 'updated_at' ? 'selected' : '' %>>更新时间</option>
                    <option value="clicks" <%= query.sortBy === 'clicks' ? 'selected' : '' %>>点击量</option>
                    <option value="title" <%= query.sortBy === 'title' ? 'selected' : '' %>>标题</option>
                </select>
            </div>
            
            <div class="col-md-1">
                <label for="order" class="form-label">顺序</label>
                <select class="form-select" id="order" name="order">
                    <option value="desc" <%= (query.order || 'desc') === 'desc' ? 'selected' : '' %>>降序</option>
                    <option value="asc" <%= query.order === 'asc' ? 'selected' : '' %>>升序</option>
                </select>
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-funnel me-1"></i>筛选
                </button>
                <a href="/livecode" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 批量操作栏 -->
<div class="batch-actions d-none mb-3">
    <div class="card">
        <div class="card-body py-2">
            <div class="d-flex justify-content-between align-items-center">
                <div class="batch-selection-info">
                    <i class="bi bi-check-square me-2"></i>
                    <span class="text-muted">已选择 0 项</span>
                </div>
                <div class="batch-action-buttons">
                    <button type="button" class="btn btn-sm btn-outline-success me-2" onclick="batchToggleStatus('active')">
                        <i class="bi bi-toggle-on me-1"></i>批量启用
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning me-2" onclick="batchToggleStatus('inactive')">
                        <i class="bi bi-toggle-off me-1"></i>批量禁用
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="batchDelete()">
                        <i class="bi bi-trash me-1"></i>批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 活码列表 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                活码列表 (共 <%= pagination.total %> 个)
            </h6>
            <div class="card-tools">
                <div class="form-check">
                    <input class="form-check-input batch-select-all" type="checkbox" id="selectAll">
                    <label class="form-check-label" for="selectAll">
                        全选
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <% if (livecodes && livecodes.length > 0) { %>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input batch-select-all">
                            </th>
                            <th>活码信息</th>
                            <th width="120">状态</th>
                            <th width="100">点击量</th>
                            <th width="120">创建时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% livecodes.forEach(function(livecode) { %>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input batch-select-item" 
                                           value="<%= livecode.id %>">
                                </td>
                                <td>
                                    <div class="livecode-info">
                                        <div class="d-flex align-items-start">
                                            <div class="livecode-qr me-3">
                                                <div class="qr-placeholder bg-light border rounded d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px; cursor: pointer;" 
                                                     onclick="showQRCode('<%= livecode.code %>')">
                                                    <i class="bi bi-qr-code text-muted"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <a href="/livecode/<%= livecode.id %>" class="text-decoration-none">
                                                        <%= livecode.title %>
                                                    </a>
                                                    <% if (livecode.category) { %>
                                                        <span class="badge bg-secondary ms-2"><%= livecode.category %></span>
                                                    <% } %>
                                                </h6>
                                                <div class="livecode-url mb-1">
                                                    <small class="text-primary">
                                                        <i class="bi bi-link-45deg me-1"></i>
                                                        <span class="livecode-short-url" onclick="copyLiveCodeUrl('<%= livecode.code %>')">
                                                            <%= request.protocol %>://<%= request.get('host') %>/<%= livecode.code %>
                                                        </span>
                                                    </small>
                                                </div>
                                                <div class="livecode-target">
                                                    <small class="text-muted">
                                                        <i class="bi bi-arrow-right me-1"></i>
                                                        <%= livecode.target_url.length > 60 ? livecode.target_url.substring(0, 60) + '...' : livecode.target_url %>
                                                    </small>
                                                </div>
                                                <% if (livecode.description) { %>
                                                    <div class="livecode-description mt-1">
                                                        <small class="text-muted">
                                                            <%= livecode.description.length > 80 ? livecode.description.substring(0, 80) + '...' : livecode.description %>
                                                        </small>
                                                    </div>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<%= livecode.is_active ? 'success' : 'secondary' %>">
                                        <%= livecode.is_active ? '启用' : '禁用' %>
                                    </span>
                                </td>
                                <td>
                                    <span class="text-primary fw-bold">
                                        <%= (livecode.click_count || 0).toLocaleString() %>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <%= new Date(livecode.created_at).toLocaleDateString('zh-CN') %>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="previewLiveCode('<%= livecode.code %>')" 
                                                data-bs-toggle="tooltip" title="预览">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <a href="/livecode/<%= livecode.id %>/edit" class="btn btn-outline-secondary" 
                                           data-bs-toggle="tooltip" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="showAnalytics('<%= livecode.id %>')" 
                                                data-bs-toggle="tooltip" title="统计">
                                            <i class="bi bi-graph-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-<%= livecode.is_active ? 'warning' : 'success' %>" 
                                                onclick="toggleLiveCodeStatus('<%= livecode.id %>', '<%= livecode.is_active ? 'active' : 'inactive' %>')" 
                                                data-bs-toggle="tooltip" title="<%= livecode.is_active ? '禁用' : '启用' %>">
                                            <i class="bi bi-toggle-<%= livecode.is_active ? 'off' : 'on' %>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteLiveCode('<%= livecode.id %>', '<%= livecode.title %>')" 
                                                data-bs-toggle="tooltip" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h5 class="mt-3 text-muted">暂无活码</h5>
                <p class="text-muted">您还没有创建任何活码，点击上方按钮开始创建。</p>
                <a href="/livecode/create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    创建第一个活码
                </a>
            </div>
        <% } %>
    </div>
    
    <!-- 分页 -->
    <% if (pagination && pagination.totalPages > 1) { %>
        <div class="card-footer">
            <nav aria-label="活码列表分页">
                <ul class="pagination justify-content-center mb-0">
                    <li class="page-item <%= pagination.currentPage === 1 ? 'disabled' : '' %>">
                        <a class="page-link" href="?page=<%= pagination.currentPage - 1 %>&<%= new URLSearchParams(query).toString() %>">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                    
                    <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                        <li class="page-item <%= i === pagination.currentPage ? 'active' : '' %>">
                            <a class="page-link" href="?page=<%= i %>&<%= new URLSearchParams(query).toString() %>">
                                <%= i %>
                            </a>
                        </li>
                    <% } %>
                    
                    <li class="page-item <%= pagination.currentPage === pagination.totalPages ? 'disabled' : '' %>">
                        <a class="page-link" href="?page=<%= pagination.currentPage + 1 %>&<%= new URLSearchParams(query).toString() %>">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="text-center mt-2">
                <small class="text-muted">
                    显示第 <%= (pagination.currentPage - 1) * pagination.pageSize + 1 %> - 
                    <%= Math.min(pagination.currentPage * pagination.pageSize, pagination.total) %> 项，
                    共 <%= pagination.total %> 项
                </small>
            </div>
        </div>
    <% } %>
</div>

<!-- 二维码模态框 -->
<div class="modal fade" id="qrModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">活码二维码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="qrcode" class="mb-3"></div>
                <p class="text-muted" id="qrUrl"></p>
                <button type="button" class="btn btn-primary" onclick="downloadQRCode()">
                    <i class="bi bi-download me-2"></i>下载二维码
                </button>
            </div>
        </div>
    </div>
</div>

<% contentFor('additionalJS') %>
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
// 显示二维码
function showQRCode(code) {
    const url = window.location.origin + '/' + code;
    const qrModal = new bootstrap.Modal(document.getElementById('qrModal'));
    
    // 清空之前的二维码
    document.getElementById('qrcode').innerHTML = '';
    document.getElementById('qrUrl').textContent = url;
    
    // 生成二维码
    QRCode.toCanvas(document.getElementById('qrcode'), url, {
        width: 256,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    }, function(error) {
        if (error) {
            console.error('生成二维码失败:', error);
            LiveCodeApp.ui.showError('生成二维码失败');
        }
    });
    
    qrModal.show();
}

// 下载二维码
function downloadQRCode() {
    const canvas = document.querySelector('#qrcode canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'qrcode.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}

// 显示统计信息
function showAnalytics(id) {
    window.location.href = '/analytics/livecode/' + id;
}

// 批量切换状态
function batchToggleStatus(status) {
    const selectedIds = $('.batch-select-item:checked').map(function() {
        return $(this).val();
    }).get();
    
    if (selectedIds.length === 0) {
        LiveCodeApp.ui.showWarning('请先选择要操作的活码');
        return;
    }
    
    const action = status === 'active' ? '启用' : '禁用';
    LiveCodeApp.ui.confirm(`确定要${action}选中的 ${selectedIds.length} 个活码吗？`).then(confirmed => {
        if (confirmed) {
            LiveCodeApp.api.put('/livecodes/batch', {
                ids: selectedIds,
                action: 'toggle_status',
                status: status
            }).then(response => {
                if (response.success) {
                    LiveCodeApp.ui.showSuccess(`批量${action}成功`);
                    window.location.reload();
                } else {
                    LiveCodeApp.ui.showError(response.message || `批量${action}失败`);
                }
            }).catch(error => {
                console.error(`批量${action}失败:`, error);
                LiveCodeApp.ui.showError(`批量${action}失败，请稍后重试`);
            });
        }
    });
}

// 批量删除
function batchDelete() {
    const selectedIds = $('.batch-select-item:checked').map(function() {
        return $(this).val();
    }).get();
    
    if (selectedIds.length === 0) {
        LiveCodeApp.ui.showWarning('请先选择要删除的活码');
        return;
    }
    
    LiveCodeApp.ui.confirm(`确定要删除选中的 ${selectedIds.length} 个活码吗？此操作不可撤销。`, '批量删除确认').then(confirmed => {
        if (confirmed) {
            LiveCodeApp.api.delete('/livecodes/batch', {
                ids: selectedIds
            }).then(response => {
                if (response.success) {
                    LiveCodeApp.ui.showSuccess('批量删除成功');
                    window.location.reload();
                } else {
                    LiveCodeApp.ui.showError(response.message || '批量删除失败');
                }
            }).catch(error => {
                console.error('批量删除失败:', error);
                LiveCodeApp.ui.showError('批量删除失败，请稍后重试');
            });
        }
    });
}

$(document).ready(function() {
    // 筛选表单提交
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const params = new URLSearchParams();
        
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                params.append(key, value);
            }
        }
        
        window.location.href = '/livecode?' + params.toString();
    });
    
    // 实时搜索
    $('#search').on('input', LiveCodeApp.utils.debounce(function() {
        $('#filterForm').submit();
    }, 500));
    
    // 复制活码链接点击事件
    $('.livecode-short-url').on('click', function() {
        $(this).addClass('text-success');
        setTimeout(() => {
            $(this).removeClass('text-success');
        }, 1000);
    });
});
</script>

<% endContentFor() %>
