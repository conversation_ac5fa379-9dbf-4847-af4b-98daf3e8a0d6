# 活码管理平台

一个功能完整的活码（动态二维码）管理系统，支持WhatsApp链接轮询、访问统计、地理位置分析等功能。

## 功能特性

### 🎯 核心功能
- **活码管理**: 创建、编辑、删除活码，支持批量操作
- **轮询系统**: 智能轮询多个WhatsApp号码，提高转化率
- **访问统计**: 实时访问数据统计和分析
- **地理分析**: 基于MaxMind的地理位置分析
- **权限管理**: 三级权限系统（super_admin, boss, manager）

### 📊 数据分析
- **实时仪表板**: 核心指标监控和趋势分析
- **访问记录**: 详细的访问日志和用户行为分析
- **地理分布**: 访客地理位置分布统计
- **设备统计**: 访问设备类型分析
- **转化分析**: 访问转化率统计

### 🔧 技术特性
- **高性能**: 支持日均20万+访问量
- **响应式设计**: 完美适配移动端和桌面端
- **安全认证**: JWT + Session双重认证
- **数据导出**: 支持CSV、PDF等格式导出
- **实时更新**: WebSocket实时数据推送

## 技术栈

### 后端技术
- **Node.js** + **Express.js**: 服务器框架
- **Better-SQLite3**: 高性能SQLite数据库
- **JWT**: 用户认证
- **Express-Validator**: 数据验证
- **MaxMind GeoLite2**: 地理位置服务

### 前端技术
- **EJS**: 模板引擎
- **Bootstrap 5.3.0**: UI框架
- **jQuery 3.7.0**: JavaScript库
- **Chart.js**: 图表库
- **QRCode.js**: 二维码生成

## 快速开始

### 环境要求
- Node.js 16.0+
- npm 8.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 活码系统-快速开发使用
```

2. **安装依赖**
```bash
npm install
```

3. **初始化数据库**
```bash
npm run init-db
```

4. **设置地理位置数据库**
```bash
npm run setup-maxmind
```

5. **启动服务**
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

6. **访问系统**
打开浏览器访问: `http://localhost:3000`

### 默认账户
- **超级管理员**: <EMAIL> / admin123
- **老板账户**: <EMAIL> / boss123  
- **经理账户**: <EMAIL> / manager123

## 项目结构

```
活码系统-快速开发使用/
├── src/                    # 源代码目录
│   ├── controllers/        # 控制器
│   ├── middleware/         # 中间件
│   ├── models/            # 数据模型
│   ├── routes/            # 路由定义
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── views/                 # 视图模板
│   ├── analytics/         # 数据分析页面
│   ├── auth/              # 认证页面
│   ├── livecode/          # 活码管理页面
│   └── partials/          # 公共组件
├── public/                # 静态资源
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── images/            # 图片资源
├── data/                  # 数据文件
├── logs/                  # 日志文件
└── scripts/               # 脚本文件
```

## 主要页面

### 🏠 首页仪表板
- 快速统计概览
- 实时状态监控
- 快速操作入口
- 最近活动记录

### 📱 活码管理
- **活码列表** (`/livecode/page`): 查看和管理所有活码
- **创建活码** (`/livecode/page/create`): 创建新的活码
- **编辑活码** (`/livecode/:id/page/edit`): 编辑活码信息
- **轮询数据** (`/livecode/:id/page/polling`): 管理轮询字符串
- **访问记录** (`/livecode/:id/page/access-logs`): 查看访问日志

### 📊 数据分析
- **数据仪表板** (`/analytics/page/dashboard`): 综合数据分析
- **访问统计**: 详细访问数据分析
- **地理分析**: 地理位置分布分析
- **报告中心**: 数据报告生成和下载

## API接口

### 活码管理API
- `GET /api/livecode` - 获取活码列表
- `POST /api/livecode` - 创建活码
- `PUT /api/livecode/:id` - 更新活码
- `DELETE /api/livecode/:id` - 删除活码

### 轮询管理API
- `GET /api/livecode/:id/polling-strings` - 获取轮询字符串
- `POST /api/livecode/:id/polling-strings` - 添加轮询字符串
- `PUT /api/livecode/:id/polling-strings/:pollingId` - 更新轮询字符串
- `DELETE /api/livecode/:id/polling-strings/:pollingId` - 删除轮询字符串

### 数据分析API
- `GET /api/analytics/dashboard` - 获取仪表板数据
- `GET /api/analytics/livecodes/:id/stats` - 获取活码统计
- `GET /api/analytics/geo` - 获取地理分析数据

## 配置说明

### 环境变量
创建 `.env` 文件并配置以下变量:

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_PATH=./data/livecode.db

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# Session配置
SESSION_SECRET=your-session-secret

# MaxMind配置
MAXMIND_LICENSE_KEY=your-maxmind-license-key
```

### 数据库配置
系统使用SQLite数据库，数据文件位于 `data/livecode.db`。

## 部署指南

### 生产环境部署

1. **设置环境变量**
```bash
export NODE_ENV=production
export PORT=3000
```

2. **使用PM2部署**
```bash
npm install -g pm2
pm2 start server.js --name "livecode-system"
```

3. **Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发指南

### 添加新功能
1. 在 `src/models/` 中定义数据模型
2. 在 `src/services/` 中实现业务逻辑
3. 在 `src/controllers/` 中创建控制器
4. 在 `src/routes/` 中定义路由
5. 在 `views/` 中创建页面模板

### 代码规范
- 使用ES6+语法
- 遵循RESTful API设计
- 统一错误处理
- 完善的输入验证
- 详细的注释说明

## 许可证

MIT License

## 支持

如有问题或建议，请提交Issue或联系开发团队。

---

**活码管理平台** - 让活码管理更简单、更高效！
