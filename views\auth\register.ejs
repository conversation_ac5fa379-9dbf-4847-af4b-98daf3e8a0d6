<%- include('../layout', {
    title: '创建用户',
    currentPage: 'users',
    pageHeader: {
        title: '创建新用户',
        subtitle: '为系统添加新的用户账户',
        actions: [
            {
                text: '返回用户列表',
                url: '/admin/users',
                class: 'btn-outline-secondary',
                icon: 'arrow-left'
            }
        ]
    },
    breadcrumb: [
        { name: '首页', url: '/admin/dashboard' },
        { name: '用户管理', url: '/admin/users' },
        { name: '创建用户' }
    ]
}) %>

<% contentFor('body') %>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-plus me-2"></i>
                    用户信息
                </h5>
            </div>
            <div class="card-body">
                <form action="/auth/register" method="POST" id="registerForm" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-1"></i>
                                    用户名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       required minlength="3" maxlength="50" 
                                       pattern="^[a-zA-Z0-9_]+$"
                                       placeholder="请输入用户名">
                                <div class="form-text">
                                    3-50个字符，只能包含字母、数字和下划线
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>
                                    邮箱地址 <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       required placeholder="请输入邮箱地址">
                                <div class="form-text">
                                    用于接收系统通知和密码重置
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>
                                    密码 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           required minlength="6" placeholder="请输入密码">
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    至少6个字符，建议包含字母、数字和特殊字符
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="bi bi-lock-fill me-1"></i>
                                    确认密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                       required placeholder="请再次输入密码">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">
                                    <i class="bi bi-shield-check me-1"></i>
                                    用户角色 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">请选择用户角色</option>
                                    <option value="manager">普通用户</option>
                                    <% if (user && user.role === 'super_admin') { %>
                                        <option value="boss">管理员</option>
                                        <option value="super_admin">超级管理员</option>
                                    <% } else if (user && user.role === 'boss') { %>
                                        <option value="boss">管理员</option>
                                    <% } %>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>普通用户:</strong> 可管理自己的活码和查看统计<br>
                                        <strong>管理员:</strong> 可管理所有活码和用户<br>
                                        <strong>超级管理员:</strong> 拥有所有权限
                                    </small>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    <i class="bi bi-toggle-on me-1"></i>
                                    账户状态
                                </label>
                                <select class="form-select" id="status" name="is_active">
                                    <option value="1" selected>启用</option>
                                    <option value="0">禁用</option>
                                </select>
                                <div class="form-text">
                                    禁用的账户无法登录系统
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="notes" class="form-label">
                            <i class="bi bi-journal-text me-1"></i>
                            备注信息
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="可选：添加关于此用户的备注信息"></textarea>
                        <div class="form-text">
                            最多500个字符
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>注意事项：</strong>
                        <ul class="mb-0 mt-2">
                            <li>用户创建后将收到包含登录信息的邮件通知</li>
                            <li>建议用户首次登录后立即修改密码</li>
                            <li>用户角色决定了其在系统中的权限范围</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/admin/users" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            创建用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<% contentFor('additionalJS') %>
<script>
$(document).ready(function() {
    // 密码显示/隐藏切换
    $('#togglePassword').on('click', function() {
        const $password = $('#password');
        const $icon = $(this).find('i');
        
        if ($password.attr('type') === 'password') {
            $password.attr('type', 'text');
            $icon.removeClass('bi-eye').addClass('bi-eye-slash');
        } else {
            $password.attr('type', 'password');
            $icon.removeClass('bi-eye-slash').addClass('bi-eye');
        }
    });
    
    // 表单验证
    const form = document.getElementById('registerForm');
    
    // 自定义验证函数
    function validateForm() {
        let isValid = true;
        
        // 验证用户名
        const username = $('#username').val();
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            $('#username').addClass('is-invalid');
            $('#username').siblings('.invalid-feedback').text('用户名只能包含字母、数字和下划线');
            isValid = false;
        } else {
            $('#username').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证邮箱
        const email = $('#email').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            $('#email').addClass('is-invalid');
            $('#email').siblings('.invalid-feedback').text('请输入有效的邮箱地址');
            isValid = false;
        } else {
            $('#email').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证密码
        const password = $('#password').val();
        if (password.length < 6) {
            $('#password').addClass('is-invalid');
            $('#password').siblings('.invalid-feedback').text('密码长度至少为6个字符');
            isValid = false;
        } else {
            $('#password').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证确认密码
        const confirmPassword = $('#confirmPassword').val();
        if (password !== confirmPassword) {
            $('#confirmPassword').addClass('is-invalid');
            $('#confirmPassword').siblings('.invalid-feedback').text('两次输入的密码不一致');
            isValid = false;
        } else {
            $('#confirmPassword').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证角色
        const role = $('#role').val();
        if (!role) {
            $('#role').addClass('is-invalid');
            $('#role').siblings('.invalid-feedback').text('请选择用户角色');
            isValid = false;
        } else {
            $('#role').removeClass('is-invalid').addClass('is-valid');
        }
        
        return isValid;
    }
    
    // 实时验证
    $('#username, #email, #password, #confirmPassword, #role').on('blur', validateForm);
    
    // 表单提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        const $submitBtn = $(form).find('button[type="submit"]');
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>创建中...');
        
        // 提交表单
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                LiveCodeApp.ui.showSuccess('用户创建成功');
                setTimeout(() => {
                    window.location.href = '/admin/users';
                }, 1500);
            } else {
                LiveCodeApp.ui.showError(data.message || '创建失败');
                $submitBtn.prop('disabled', false);
                $submitBtn.html('<i class="bi bi-person-plus me-2"></i>创建用户');
            }
        })
        .catch(error => {
            console.error('创建用户失败:', error);
            LiveCodeApp.ui.showError('创建失败，请稍后重试');
            $submitBtn.prop('disabled', false);
            $submitBtn.html('<i class="bi bi-person-plus me-2"></i>创建用户');
        });
    });
    
    // 自动聚焦到用户名输入框
    $('#username').focus();
});
</script>

<% endContentFor() %>
