/* 活码管理平台 - 主样式文件 */

/* ===== 全局变量 ===== */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    --sidebar-width: 250px;
    --navbar-height: 56px;
    --quick-actions-height: 48px;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.3s ease;
}

/* ===== 基础样式重置 ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f5f5;
}

/* ===== 导航栏样式 ===== */
.navbar {
    box-shadow: var(--box-shadow);
    z-index: 1030;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* 用户头像样式 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar i {
    font-size: 1.2rem;
}

.user-info .user-name {
    font-size: 0.875rem;
    font-weight: 600;
}

.user-info .user-role {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* ===== 快速操作栏 ===== */
.quick-actions-bar {
    height: var(--quick-actions-height);
    border-bottom: 1px solid #dee2e6;
}

.search-container {
    max-width: 300px;
}

.status-indicators .badge {
    font-size: 0.75rem;
}

/* ===== 侧边栏样式 ===== */
.sidebar {
    background: #fff;
    border-right: 1px solid #dee2e6;
    min-height: calc(100vh - var(--navbar-height) - var(--quick-actions-height));
    padding: 1rem 0;
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
}

/* ===== 主内容区域 ===== */
.main-content {
    padding: 1.5rem;
    min-height: calc(100vh - var(--navbar-height) - var(--quick-actions-height));
}

/* ===== 页面头部 ===== */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-header h1 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-header .subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
    margin-bottom: 0;
}

/* ===== 卡片样式 ===== */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* 统计卡片 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    color: var(--secondary-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-card .stats-change {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.stats-card.stats-primary .stats-number { color: var(--primary-color); }
.stats-card.stats-success .stats-number { color: var(--success-color); }
.stats-card.stats-warning .stats-number { color: var(--warning-color); }
.stats-card.stats-danger .stats-number { color: var(--danger-color); }
.stats-card.stats-info .stats-number { color: var(--info-color); }

/* ===== 表格样式 ===== */
.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.table td {
    vertical-align: middle;
}

/* ===== 按钮样式 ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* ===== 表单样式 ===== */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== 加载动画 ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

/* ===== 通知样式 ===== */
.notification-dropdown {
    min-width: 300px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    transition: var(--transition);
}

.notification-item:hover {
    background-color: var(--light-color);
}

.notification-item:last-child {
    border-bottom: none;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: var(--navbar-height);
        left: -100%;
        width: var(--sidebar-width);
        height: calc(100vh - var(--navbar-height));
        z-index: 1020;
        transition: var(--transition);
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .quick-actions-bar .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-container {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .stats-card .stats-number {
        font-size: 2rem;
    }

    .page-header h1 {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* ===== 活码特定样式 ===== */
.livecode-card {
    transition: var(--transition);
    cursor: pointer;
}

.livecode-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.livecode-status {
    position: absolute;
    top: 10px;
    right: 10px;
}

.livecode-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    background: var(--light-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.livecode-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.livecode-stat {
    text-align: center;
    flex: 1;
}

.livecode-stat-number {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.livecode-stat-label {
    font-size: 0.75rem;
    color: var(--secondary-color);
    text-transform: uppercase;
}

/* ===== 轮询字符串样式 ===== */
.polling-string-item {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background: #fff;
    transition: var(--transition);
}

.polling-string-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow);
}

.polling-string-item.active {
    border-color: var(--success-color);
    background: rgba(25, 135, 84, 0.05);
}

.polling-string-item.inactive {
    opacity: 0.6;
    background: var(--light-color);
}

.polling-string-value {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    word-break: break-all;
}

.polling-string-meta {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
}

/* ===== 批量操作样式 ===== */
.batch-actions {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
}

.batch-actions.show {
    display: block !important;
}

.batch-selection-info {
    font-weight: 600;
    color: var(--primary-color);
}

/* ===== 图表容器样式 ===== */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.chart-container.large {
    height: 400px;
}

.chart-container.small {
    height: 200px;
}

/* ===== 地理位置样式 ===== */
.geo-stats-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.geo-stats-item:last-child {
    border-bottom: none;
}

.geo-country {
    display: flex;
    align-items: center;
}

.geo-flag {
    width: 24px;
    height: 16px;
    margin-right: 0.5rem;
    border-radius: 2px;
}

.geo-progress {
    flex: 1;
    margin: 0 1rem;
}

.geo-count {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 60px;
    text-align: right;
}

/* ===== 时间线样式 ===== */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid #fff;
    box-shadow: var(--box-shadow);
}

.timeline-content {
    background: #fff;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.timeline-time {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

/* ===== 状态指示器 ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-indicator.status-active {
    background: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.status-indicator.status-inactive {
    background: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.status-indicator.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-indicator.status-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-indicator i {
    margin-right: 0.25rem;
}

/* ===== 搜索和筛选样式 ===== */
.search-filters {
    background: #fff;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.filter-tag {
    background: var(--primary-color);
    color: #fff;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.filter-tag .remove {
    cursor: pointer;
    opacity: 0.8;
}

.filter-tag .remove:hover {
    opacity: 1;
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--secondary-color);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.empty-state-description {
    margin-bottom: 1.5rem;
}

/* ===== 工具提示样式 ===== */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* ===== 进度条样式 ===== */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: var(--light-color);
}

.progress-bar {
    border-radius: 4px;
}

/* ===== 徽章样式 ===== */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
}

.badge.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* ===== 分页样式 ===== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.page-link:hover {
    background-color: var(--light-color);
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ===== 模态框样式 ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* ===== 下拉菜单样式 ===== */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--light-color);
}

.dropdown-item.active {
    background-color: var(--primary-color);
}

/* ===== 面包屑样式 ===== */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--secondary-color);
}

.breadcrumb-item.active {
    color: var(--secondary-color);
}

/* ===== 警告框样式 ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-primary { border-left-color: var(--primary-color); }
.alert-success { border-left-color: var(--success-color); }
.alert-warning { border-left-color: var(--warning-color); }
.alert-danger { border-left-color: var(--danger-color); }
.alert-info { border-left-color: var(--info-color); }
