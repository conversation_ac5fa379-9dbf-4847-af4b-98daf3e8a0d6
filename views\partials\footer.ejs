<!-- 页脚 -->
<footer class="footer mt-auto py-4 bg-light border-top">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="footer-info">
                    <h6 class="mb-1">活码管理平台</h6>
                    <p class="text-muted small mb-0">
                        高性能URL重定向服务 - 让链接管理更简单
                    </p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="footer-links text-md-end">
                    <div class="d-flex flex-column flex-md-row justify-content-md-end align-items-md-center">
                        <div class="footer-stats mb-2 mb-md-0 me-md-4">
                            <small class="text-muted">
                                <span class="me-3">
                                    <i class="bi bi-server me-1"></i>
                                    服务器时间: <span id="serverTime"><%= new Date().toLocaleString('zh-CN') %></span>
                                </span>
                                <span class="me-3">
                                    <i class="bi bi-speedometer2 me-1"></i>
                                    运行时间: <span id="uptime">--</span>
                                </span>
                            </small>
                        </div>
                        <div class="footer-actions">
                            <a href="/help" class="text-decoration-none me-3" target="_blank">
                                <small><i class="bi bi-question-circle me-1"></i>帮助</small>
                            </a>
                            <a href="/api/docs" class="text-decoration-none me-3" target="_blank">
                                <small><i class="bi bi-code-square me-1"></i>API</small>
                            </a>
                            <a href="/about" class="text-decoration-none">
                                <small><i class="bi bi-info-circle me-1"></i>关于</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <hr class="my-3">
        
        <div class="row align-items-center">
            <div class="col-md-6">
                <small class="text-muted">
                    © 2024 活码管理平台. 版本 1.0.0
                </small>
            </div>
            <div class="col-md-6">
                <div class="text-md-end">
                    <small class="text-muted">
                        <span class="me-3">
                            <i class="bi bi-shield-check me-1"></i>
                            安全连接
                        </span>
                        <span class="me-3">
                            <i class="bi bi-lightning-charge me-1"></i>
                            高性能
                        </span>
                        <span>
                            <i class="bi bi-heart-fill text-danger me-1"></i>
                            用心制作
                        </span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</footer>

<script>
// 更新服务器时间
function updateServerTime() {
    document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
}

// 更新运行时间
async function updateUptime() {
    try {
        const response = await fetch('/api/health');
        const data = await response.json();
        if (data.uptime) {
            const uptime = Math.floor(data.uptime);
            const days = Math.floor(uptime / 86400);
            const hours = Math.floor((uptime % 86400) / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            
            let uptimeText = '';
            if (days > 0) uptimeText += days + '天 ';
            if (hours > 0) uptimeText += hours + '小时 ';
            uptimeText += minutes + '分钟';
            
            document.getElementById('uptime').textContent = uptimeText;
        }
    } catch (error) {
        console.error('获取运行时间失败:', error);
    }
}

// 页面加载完成后开始更新
document.addEventListener('DOMContentLoaded', function() {
    updateServerTime();
    updateUptime();
    
    // 每分钟更新一次时间
    setInterval(updateServerTime, 60000);
    
    // 每5分钟更新一次运行时间
    setInterval(updateUptime, 300000);
});
</script>

<style>
.footer {
    background-color: #f8f9fa !important;
    border-top: 1px solid #dee2e6;
}

.footer-info h6 {
    color: #495057;
    font-weight: 600;
}

.footer-links a {
    color: #6c757d;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #495057;
}

.footer-stats {
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .footer-stats {
        text-align: center;
    }
    
    .footer-actions {
        text-align: center;
    }
    
    .footer-stats span {
        display: block;
        margin-bottom: 0.25rem;
    }
}
</style>
