<%- include('../layout', {
    title: '个人资料',
    currentPage: 'profile',
    pageHeader: {
        title: '个人资料',
        subtitle: '管理您的账户信息和偏好设置'
    },
    breadcrumb: [
        { name: '首页', url: '/admin/dashboard' },
        { name: '个人资料' }
    ]
}) %>

<% contentFor('body') %>

<div class="row">
    <div class="col-lg-4">
        <!-- 用户信息卡片 -->
        <div class="card">
            <div class="card-body text-center">
                <div class="user-avatar mb-3">
                    <div class="avatar-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px; border-radius: 50%; font-size: 2rem; font-weight: bold;">
                        <%= user.username.charAt(0).toUpperCase() %>
                    </div>
                </div>
                <h5 class="card-title mb-1"><%= user.username %></h5>
                <p class="text-muted mb-2"><%= user.email %></p>
                <span class="badge bg-<%= user.role === 'super_admin' ? 'danger' : user.role === 'boss' ? 'warning' : 'primary' %> mb-3">
                    <%= user.role === 'super_admin' ? '超级管理员' : user.role === 'boss' ? '管理员' : '普通用户' %>
                </span>
                
                <div class="user-stats">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <h6 class="stat-number text-primary"><%= user.livecode_count || 0 %></h6>
                                <small class="text-muted">活码数量</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h6 class="stat-number text-success"><%= user.total_clicks || 0 %></h6>
                                <small class="text-muted">总点击量</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h6 class="stat-number text-info"><%= Math.floor((Date.now() - new Date(user.created_at)) / (1000 * 60 * 60 * 24)) %></h6>
                                <small class="text-muted">注册天数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 账户状态 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    账户状态
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>账户状态</span>
                    <span class="badge bg-<%= user.is_active ? 'success' : 'danger' %>">
                        <%= user.is_active ? '正常' : '已禁用' %>
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>注册时间</span>
                    <small class="text-muted"><%= new Date(user.created_at).toLocaleDateString('zh-CN') %></small>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>最后登录</span>
                    <small class="text-muted">
                        <%= user.last_login ? new Date(user.last_login).toLocaleDateString('zh-CN') : '从未登录' %>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- 基本信息编辑 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-person-gear me-2"></i>
                    基本信息
                </h6>
            </div>
            <div class="card-body">
                <form id="profileForm" action="/auth/profile" method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<%= user.username %>" readonly>
                                <div class="form-text">用户名无法修改</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<%= user.email %>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">个人简介</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="介绍一下自己..."><%= user.notes || '' %></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg me-2"></i>
                        保存更改
                    </button>
                </form>
            </div>
        </div>
        
        <!-- 修改密码 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-key me-2"></i>
                    修改密码
                </h6>
            </div>
            <div class="card-body">
                <form id="passwordForm" action="/auth/change-password" method="POST">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="newPassword" name="newPassword" 
                                       required minlength="6">
                                <div class="form-text">至少6个字符</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirmNewPassword" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirmNewPassword" name="confirmNewPassword" required>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-shield-lock me-2"></i>
                        修改密码
                    </button>
                </form>
            </div>
        </div>
        
        <!-- 偏好设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    偏好设置
                </h6>
            </div>
            <div class="card-body">
                <form id="preferencesForm" action="/auth/preferences" method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">时区</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="Asia/Shanghai" <%= (user.timezone || 'Asia/Shanghai') === 'Asia/Shanghai' ? 'selected' : '' %>>
                                        中国标准时间 (UTC+8)
                                    </option>
                                    <option value="UTC" <%= user.timezone === 'UTC' ? 'selected' : '' %>>
                                        协调世界时 (UTC)
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label">语言</label>
                                <select class="form-select" id="language" name="language">
                                    <option value="zh-CN" <%= (user.language || 'zh-CN') === 'zh-CN' ? 'selected' : '' %>>
                                        简体中文
                                    </option>
                                    <option value="en-US" <%= user.language === 'en-US' ? 'selected' : '' %>>
                                        English
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" 
                                   name="email_notifications" <%= user.email_notifications ? 'checked' : '' %>>
                            <label class="form-check-label" for="emailNotifications">
                                接收邮件通知
                            </label>
                        </div>
                        <div class="form-text">包括系统更新、安全提醒等重要通知</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg me-2"></i>
                        保存偏好
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<% contentFor('additionalJS') %>
<script>
$(document).ready(function() {
    // 基本信息表单提交
    $('#profileForm').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>保存中...');
        
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                LiveCodeApp.ui.showSuccess('个人信息更新成功');
            } else {
                LiveCodeApp.ui.showError(data.message || '更新失败');
            }
        })
        .catch(error => {
            console.error('更新失败:', error);
            LiveCodeApp.ui.showError('更新失败，请稍后重试');
        })
        .finally(() => {
            $submitBtn.prop('disabled', false);
            $submitBtn.html('<i class="bi bi-check-lg me-2"></i>保存更改');
        });
    });
    
    // 密码修改表单提交
    $('#passwordForm').on('submit', function(e) {
        e.preventDefault();
        
        const newPassword = $('#newPassword').val();
        const confirmNewPassword = $('#confirmNewPassword').val();
        
        if (newPassword !== confirmNewPassword) {
            LiveCodeApp.ui.showError('两次输入的新密码不一致');
            return;
        }
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>修改中...');
        
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                LiveCodeApp.ui.showSuccess('密码修改成功');
                $form[0].reset();
            } else {
                LiveCodeApp.ui.showError(data.message || '密码修改失败');
            }
        })
        .catch(error => {
            console.error('密码修改失败:', error);
            LiveCodeApp.ui.showError('密码修改失败，请稍后重试');
        })
        .finally(() => {
            $submitBtn.prop('disabled', false);
            $submitBtn.html('<i class="bi bi-shield-lock me-2"></i>修改密码');
        });
    });
    
    // 偏好设置表单提交
    $('#preferencesForm').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>保存中...');
        
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                LiveCodeApp.ui.showSuccess('偏好设置保存成功');
            } else {
                LiveCodeApp.ui.showError(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            LiveCodeApp.ui.showError('保存失败，请稍后重试');
        })
        .finally(() => {
            $submitBtn.prop('disabled', false);
            $submitBtn.html('<i class="bi bi-check-lg me-2"></i>保存偏好');
        });
    });
});
</script>

<% endContentFor() %>
