<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 活码管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-brand {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .login-brand h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .login-brand p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .login-form {
            padding: 3rem 2rem;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(13, 110, 253, 0.3);
        }
        
        .login-footer {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
            margin-top: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 2rem 0 0 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list i {
            margin-right: 0.75rem;
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                border-radius: 0.5rem;
            }
            
            .login-brand {
                padding: 2rem 1.5rem;
            }
            
            .login-brand h1 {
                font-size: 2rem;
            }
            
            .login-form {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- 左侧品牌区域 -->
                <div class="col-lg-6">
                    <div class="login-brand h-100 d-flex flex-column justify-content-center">
                        <div>
                            <i class="bi bi-link-45deg" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h1>活码管理平台</h1>
                            <p>高性能URL重定向服务</p>
                            
                            <ul class="feature-list">
                                <li>
                                    <i class="bi bi-lightning-charge"></i>
                                    高性能重定向服务
                                </li>
                                <li>
                                    <i class="bi bi-arrow-repeat"></i>
                                    智能轮询管理
                                </li>
                                <li>
                                    <i class="bi bi-graph-up"></i>
                                    实时数据分析
                                </li>
                                <li>
                                    <i class="bi bi-geo-alt"></i>
                                    地理位置统计
                                </li>
                                <li>
                                    <i class="bi bi-shield-check"></i>
                                    安全可靠保障
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧登录表单 -->
                <div class="col-lg-6">
                    <div class="login-form">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-dark">欢迎回来</h2>
                            <p class="text-muted">请登录您的账户</p>
                        </div>
                        
                        <!-- Flash Messages -->
                        <%- include('../partials/flash-messages') %>
                        
                        <form action="/auth/login" method="POST" id="loginForm">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="用户名" required autocomplete="username">
                                <label for="username">
                                    <i class="bi bi-person me-2"></i>用户名
                                </label>
                            </div>
                            
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="密码" required autocomplete="current-password">
                                <label for="password">
                                    <i class="bi bi-lock me-2"></i>密码
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    记住我
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-login w-100">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                登录
                            </button>
                        </form>
                        
                        <div class="login-footer">
                            <p class="text-muted small">
                                <i class="bi bi-info-circle me-1"></i>
                                如需账户请联系系统管理员
                            </p>
                            <div class="mt-3">
                                <a href="/help" class="text-decoration-none me-3" target="_blank">
                                    <small><i class="bi bi-question-circle me-1"></i>使用帮助</small>
                                </a>
                                <a href="/api/docs" class="text-decoration-none" target="_blank">
                                    <small><i class="bi bi-code-square me-1"></i>API文档</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 表单提交处理
            $('#loginForm').on('submit', function(e) {
                const $form = $(this);
                const $submitBtn = $form.find('button[type="submit"]');
                
                // 禁用提交按钮防止重复提交
                $submitBtn.prop('disabled', true);
                $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>登录中...');
                
                // 如果有JavaScript错误，重新启用按钮
                setTimeout(function() {
                    $submitBtn.prop('disabled', false);
                    $submitBtn.html('<i class="bi bi-box-arrow-in-right me-2"></i>登录');
                }, 5000);
            });
            
            // 自动聚焦到用户名输入框
            $('#username').focus();
            
            // 回车键快捷登录
            $('#username, #password').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#loginForm').submit();
                }
            });
            
            // 密码显示/隐藏切换
            const togglePassword = $('<button type="button" class="btn btn-outline-secondary position-absolute end-0 top-50 translate-middle-y me-3" style="border: none; background: none; z-index: 10;"><i class="bi bi-eye"></i></button>');
            
            $('#password').parent().css('position', 'relative').append(togglePassword);
            
            togglePassword.on('click', function() {
                const $password = $('#password');
                const $icon = $(this).find('i');
                
                if ($password.attr('type') === 'password') {
                    $password.attr('type', 'text');
                    $icon.removeClass('bi-eye').addClass('bi-eye-slash');
                } else {
                    $password.attr('type', 'password');
                    $icon.removeClass('bi-eye-slash').addClass('bi-eye');
                }
            });
        });
    </script>
</body>
</html>
