<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand fw-bold" href="<%= typeof user !== 'undefined' ? '/admin/dashboard' : '/' %>">
            <i class="bi bi-link-45deg me-2"></i>
            活码管理平台
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Items -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <% if (typeof user !== 'undefined') { %>
                <!-- Authenticated Navigation -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'livecodes' ? 'active' : '' %>" href="/livecode">
                            <i class="bi bi-qr-code me-1"></i>
                            活码管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'analytics' ? 'active' : '' %>" href="/analytics">
                            <i class="bi bi-graph-up me-1"></i>
                            数据分析
                        </a>
                    </li>
                    <% if (['super_admin', 'boss'].includes(user.role)) { %>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-gear me-1"></i>
                                系统管理
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/admin/users">
                                    <i class="bi bi-people me-2"></i>用户管理
                                </a></li>
                                <li><a class="dropdown-item" href="/admin/logs">
                                    <i class="bi bi-journal-text me-2"></i>系统日志
                                </a></li>
                                <li><a class="dropdown-item" href="/geolocation">
                                    <i class="bi bi-geo-alt me-2"></i>地理位置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin/settings">
                                    <i class="bi bi-sliders me-2"></i>系统设置
                                </a></li>
                            </ul>
                        </li>
                    <% } %>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount" style="display: none;">
                                0
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">通知</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li id="noNotifications" class="dropdown-item-text text-muted">暂无新通知</li>
                            <div id="notificationList"></div>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="/notifications">查看所有通知</a></li>
                        </ul>
                    </li>

                    <!-- User Profile -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <div class="user-info d-none d-md-block">
                                <div class="user-name"><%= user.username %></div>
                                <div class="user-role text-muted small">
                                    <% if (user.role === 'super_admin') { %>
                                        超级管理员
                                    <% } else if (user.role === 'boss') { %>
                                        管理员
                                    <% } else { %>
                                        普通用户
                                    <% } %>
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/auth/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/auth/change-password">
                                <i class="bi bi-key me-2"></i>修改密码
                            </a></li>
                            <li><a class="dropdown-item" href="/auth/settings">
                                <i class="bi bi-gear me-2"></i>账户设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            <% } else { %>
                <!-- Public Navigation -->
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/login">
                            <i class="bi bi-box-arrow-in-right me-1"></i>
                            登录
                        </a>
                    </li>
                </ul>
            <% } %>
        </div>
    </div>
</nav>

<% if (typeof user !== 'undefined') { %>
    <!-- Quick Actions Bar (for authenticated users) -->
    <div class="quick-actions-bar bg-light border-bottom py-2">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="createLiveCode()">
                            <i class="bi bi-plus-circle me-1"></i>
                            新建活码
                        </button>
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="importData()">
                            <i class="bi bi-upload me-1"></i>
                            导入数据
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="showHelp()">
                            <i class="bi bi-question-circle me-1"></i>
                            帮助
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-end">
                        <!-- Search Bar -->
                        <div class="search-container me-3">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" placeholder="搜索活码..." id="globalSearch">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Status Indicators -->
                        <div class="status-indicators d-flex align-items-center">
                            <span class="badge bg-success me-2" id="systemStatus">
                                <i class="bi bi-circle-fill me-1"></i>
                                系统正常
                            </span>
                            <span class="text-muted small" id="lastUpdate">
                                最后更新: <span id="lastUpdateTime">--</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<% } %>
