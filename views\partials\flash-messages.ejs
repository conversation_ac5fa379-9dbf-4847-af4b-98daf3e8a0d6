<!-- Flash Messages -->
<% if (typeof messages !== 'undefined' && messages) { %>
    <div class="flash-messages mb-3">
        <!-- 成功消息 -->
        <% if (messages.success && messages.success.length > 0) { %>
            <% messages.success.forEach(function(message) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }); %>
        <% } %>
        
        <!-- 错误消息 -->
        <% if (messages.error && messages.error.length > 0) { %>
            <% messages.error.forEach(function(message) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }); %>
        <% } %>
        
        <!-- 警告消息 -->
        <% if (messages.warning && messages.warning.length > 0) { %>
            <% messages.warning.forEach(function(message) { %>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }); %>
        <% } %>
        
        <!-- 信息消息 -->
        <% if (messages.info && messages.info.length > 0) { %>
            <% messages.info.forEach(function(message) { %>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <%= message %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% }); %>
        <% } %>
    </div>
<% } %>

<!-- 验证错误消息 -->
<% if (typeof errors !== 'undefined' && errors && errors.length > 0) { %>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <strong>输入验证失败：</strong>
        <ul class="mb-0 mt-2">
            <% errors.forEach(function(error) { %>
                <li><%= error.msg %></li>
            <% }); %>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<% } %>

<script>
// 自动隐藏成功消息
document.addEventListener('DOMContentLoaded', function() {
    const successAlerts = document.querySelectorAll('.alert-success');
    successAlerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000); // 5秒后自动关闭
    });
});
</script>
