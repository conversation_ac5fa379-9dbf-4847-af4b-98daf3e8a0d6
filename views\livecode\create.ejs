<%- include('../layout', {
    title: '创建活码',
    currentPage: 'livecode-create',
    pageHeader: {
        title: '创建活码',
        subtitle: '创建新的活码链接',
        actions: [
            {
                text: '返回列表',
                url: '/livecode',
                class: 'btn-outline-secondary',
                icon: 'arrow-left'
            }
        ]
    },
    breadcrumb: [
        { name: '首页', url: '/admin/dashboard' },
        { name: '活码管理', url: '/livecode' },
        { name: '创建活码' }
    ]
}) %>

<% contentFor('body') %>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-plus-circle me-2"></i>
                    基本信息
                </h6>
            </div>
            <div class="card-body">
                <form id="createForm" action="/livecode" method="POST" novalidate>
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="bi bi-tag me-1"></i>
                            活码标题 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title" 
                               required maxlength="100" placeholder="请输入活码标题">
                        <div class="form-text">用于识别和管理活码，最多100个字符</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    <i class="bi bi-link-45deg me-1"></i>
                                    活码代码 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <%= request.protocol %>://<%= request.get('host') %>/
                                    </span>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           required pattern="^[a-zA-Z0-9_-]+$" minlength="3" maxlength="50"
                                           placeholder="请输入活码代码">
                                    <button type="button" class="btn btn-outline-secondary" id="generateCode">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                                <div class="form-text">3-50个字符，只能包含字母、数字、下划线和连字符</div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">
                                    <i class="bi bi-folder me-1"></i>
                                    分类
                                </label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">请选择分类</option>
                                    <option value="promotion">推广</option>
                                    <option value="product">产品</option>
                                    <option value="service">服务</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="target_url" class="form-label">
                            <i class="bi bi-globe me-1"></i>
                            目标URL <span class="text-danger">*</span>
                        </label>
                        <input type="url" class="form-control" id="target_url" name="target_url" 
                               required placeholder="https://example.com">
                        <div class="form-text">活码重定向的目标地址，必须以http://或https://开头</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="bi bi-journal-text me-1"></i>
                            描述信息
                        </label>
                        <textarea class="form-control" id="description" name="description" 
                                  rows="3" maxlength="500" placeholder="可选：添加活码的描述信息"></textarea>
                        <div class="form-text">最多500个字符</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expires_at" class="form-label">
                                    <i class="bi bi-calendar-event me-1"></i>
                                    过期时间
                                </label>
                                <input type="datetime-local" class="form-control" id="expires_at" name="expires_at">
                                <div class="form-text">留空表示永不过期</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_clicks" class="form-label">
                                    <i class="bi bi-hash me-1"></i>
                                    最大点击次数
                                </label>
                                <input type="number" class="form-control" id="max_clicks" name="max_clicks" 
                                       min="1" placeholder="留空表示无限制">
                                <div class="form-text">达到限制后活码将自动失效</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                <i class="bi bi-toggle-on me-1"></i>
                                立即启用活码
                            </label>
                        </div>
                        <div class="form-text">取消勾选将创建但不启用活码</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/livecode" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            创建活码
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 预览卡片 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-eye me-2"></i>
                    预览
                </h6>
            </div>
            <div class="card-body">
                <div class="preview-section">
                    <div class="mb-3">
                        <label class="form-label small text-muted">活码链接</label>
                        <div class="preview-url bg-light p-2 rounded border">
                            <small id="previewUrl" class="text-primary">
                                <%= request.protocol %>://<%= request.get('host') %>/
                            </small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label small text-muted">二维码</label>
                        <div class="text-center">
                            <div id="qrcode" class="d-inline-block"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label small text-muted">目标地址</label>
                        <div class="preview-target bg-light p-2 rounded border">
                            <small id="previewTarget" class="text-muted">请输入目标URL</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高级设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    高级设置
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enable_password" name="enable_password">
                        <label class="form-check-label" for="enable_password">
                            启用访问密码
                        </label>
                    </div>
                </div>
                
                <div class="mb-3 password-section d-none">
                    <label for="access_password" class="form-label">访问密码</label>
                    <input type="text" class="form-control" id="access_password" name="access_password" 
                           placeholder="设置访问密码">
                    <div class="form-text">用户需要输入密码才能访问</div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enable_geo_restriction" name="enable_geo_restriction">
                        <label class="form-check-label" for="enable_geo_restriction">
                            启用地理限制
                        </label>
                    </div>
                </div>
                
                <div class="mb-3 geo-section d-none">
                    <label for="allowed_countries" class="form-label">允许的国家/地区</label>
                    <select class="form-select" id="allowed_countries" name="allowed_countries" multiple>
                        <option value="CN">中国</option>
                        <option value="US">美国</option>
                        <option value="JP">日本</option>
                        <option value="KR">韩国</option>
                        <option value="SG">新加坡</option>
                    </select>
                    <div class="form-text">只有选中地区的用户可以访问</div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enable_device_restriction" name="enable_device_restriction">
                        <label class="form-check-label" for="enable_device_restriction">
                            启用设备限制
                        </label>
                    </div>
                </div>
                
                <div class="device-section d-none">
                    <label class="form-label">允许的设备类型</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="allow_mobile" name="allowed_devices" value="mobile">
                        <label class="form-check-label" for="allow_mobile">移动设备</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="allow_desktop" name="allowed_devices" value="desktop">
                        <label class="form-check-label" for="allow_desktop">桌面设备</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="allow_tablet" name="allowed_devices" value="tablet">
                        <label class="form-check-label" for="allow_tablet">平板设备</label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 提示信息 -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="text-primary">
                    <i class="bi bi-lightbulb me-2"></i>
                    创建提示
                </h6>
                <ul class="list-unstyled small text-muted mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        活码代码一旦创建不可修改
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        可以随时修改目标URL和其他设置
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        支持设置过期时间和点击限制
                    </li>
                    <li>
                        <i class="bi bi-check-circle text-success me-2"></i>
                        提供详细的访问统计分析
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<% contentFor('additionalJS') %>
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
$(document).ready(function() {
    // 生成随机代码
    $('#generateCode').on('click', function() {
        const code = LiveCodeApp.utils.generateId(8);
        $('#code').val(code).trigger('input');
    });
    
    // 实时预览
    function updatePreview() {
        const code = $('#code').val();
        const targetUrl = $('#target_url').val();
        const baseUrl = '<%= request.protocol %>://<%= request.get("host") %>/';
        
        if (code) {
            const fullUrl = baseUrl + code;
            $('#previewUrl').text(fullUrl);
            
            // 生成二维码
            $('#qrcode').empty();
            if (code.length >= 3) {
                QRCode.toCanvas(document.createElement('canvas'), fullUrl, {
                    width: 150,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function(error, canvas) {
                    if (!error) {
                        $('#qrcode').append(canvas);
                    }
                });
            }
        } else {
            $('#previewUrl').text(baseUrl);
            $('#qrcode').empty();
        }
        
        if (targetUrl) {
            $('#previewTarget').text(targetUrl);
        } else {
            $('#previewTarget').text('请输入目标URL');
        }
    }
    
    $('#code, #target_url').on('input', updatePreview);
    
    // 高级设置切换
    $('#enable_password').on('change', function() {
        if ($(this).is(':checked')) {
            $('.password-section').removeClass('d-none');
        } else {
            $('.password-section').addClass('d-none');
            $('#access_password').val('');
        }
    });
    
    $('#enable_geo_restriction').on('change', function() {
        if ($(this).is(':checked')) {
            $('.geo-section').removeClass('d-none');
        } else {
            $('.geo-section').addClass('d-none');
            $('#allowed_countries').val([]);
        }
    });
    
    $('#enable_device_restriction').on('change', function() {
        if ($(this).is(':checked')) {
            $('.device-section').removeClass('d-none');
        } else {
            $('.device-section').addClass('d-none');
            $('input[name="allowed_devices"]').prop('checked', false);
        }
    });
    
    // 表单验证
    function validateForm() {
        let isValid = true;
        
        // 验证标题
        const title = $('#title').val().trim();
        if (!title) {
            $('#title').addClass('is-invalid');
            $('#title').siblings('.invalid-feedback').text('请输入活码标题');
            isValid = false;
        } else {
            $('#title').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证代码
        const code = $('#code').val().trim();
        if (!code || !/^[a-zA-Z0-9_-]+$/.test(code) || code.length < 3) {
            $('#code').addClass('is-invalid');
            $('#code').siblings('.invalid-feedback').text('活码代码格式不正确');
            isValid = false;
        } else {
            $('#code').removeClass('is-invalid').addClass('is-valid');
        }
        
        // 验证目标URL
        const targetUrl = $('#target_url').val().trim();
        if (!targetUrl || !LiveCodeApp.utils.validateUrl(targetUrl)) {
            $('#target_url').addClass('is-invalid');
            $('#target_url').siblings('.invalid-feedback').text('请输入有效的目标URL');
            isValid = false;
        } else {
            $('#target_url').removeClass('is-invalid').addClass('is-valid');
        }
        
        return isValid;
    }
    
    // 实时验证
    $('#title, #code, #target_url').on('blur', validateForm);
    
    // 表单提交
    $('#createForm').on('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>创建中...');
        
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                LiveCodeApp.ui.showSuccess('活码创建成功');
                setTimeout(() => {
                    window.location.href = '/livecode';
                }, 1500);
            } else {
                LiveCodeApp.ui.showError(data.message || '创建失败');
                $submitBtn.prop('disabled', false);
                $submitBtn.html('<i class="bi bi-plus-circle me-2"></i>创建活码');
            }
        })
        .catch(error => {
            console.error('创建活码失败:', error);
            LiveCodeApp.ui.showError('创建失败，请稍后重试');
            $submitBtn.prop('disabled', false);
            $submitBtn.html('<i class="bi bi-plus-circle me-2"></i>创建活码');
        });
    });
    
    // 初始化预览
    updatePreview();
    
    // 自动聚焦到标题输入框
    $('#title').focus();
});
</script>

<% endContentFor() %>
