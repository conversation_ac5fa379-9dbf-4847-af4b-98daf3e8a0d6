/**
 * 活码管理平台 - 主应用JavaScript文件
 */

// 全局应用对象
window.LiveCodeApp = {
    // 配置
    config: {
        apiBaseUrl: '/api/v1',
        refreshInterval: 30000, // 30秒
        maxRetries: 3,
        retryDelay: 1000
    },
    
    // 状态管理
    state: {
        user: null,
        permissions: null,
        notifications: [],
        isLoading: false
    },
    
    // 工具函数
    utils: {},
    
    // API调用
    api: {},
    
    // UI组件
    ui: {},
    
    // 初始化
    init: function() {
        this.initEventListeners();
        this.initTooltips();
        this.initNotifications();
        this.loadUserInfo();
        this.startPeriodicUpdates();
    },
    
    // 初始化事件监听器
    initEventListeners: function() {
        // 全局搜索
        $('#globalSearch').on('input', this.utils.debounce(this.handleGlobalSearch, 300));
        
        // 确认模态框
        $('#confirmModal').on('show.bs.modal', this.handleConfirmModal);
        $('#confirmButton').on('click', this.handleConfirmAction);
        
        // 侧边栏切换（移动端）
        $('.navbar-toggler').on('click', this.toggleSidebar);
        
        // 表单提交拦截
        $('form[data-ajax="true"]').on('submit', this.handleAjaxForm);
        
        // 批量操作
        $('.batch-select-all').on('change', this.handleBatchSelectAll);
        $('.batch-select-item').on('change', this.handleBatchSelectItem);
        
        // 页面卸载前确认
        window.addEventListener('beforeunload', this.handleBeforeUnload);
    },
    
    // 初始化工具提示
    initTooltips: function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // 初始化通知系统
    initNotifications: function() {
        this.loadNotifications();
        setInterval(() => {
            this.loadNotifications();
        }, this.config.refreshInterval);
    },
    
    // 加载用户信息
    loadUserInfo: async function() {
        try {
            const response = await this.api.get('/auth/profile');
            if (response.success) {
                this.state.user = response.data;
                this.loadUserPermissions();
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    },
    
    // 加载用户权限
    loadUserPermissions: async function() {
        try {
            const response = await this.api.get('/users/permissions');
            if (response.success) {
                this.state.permissions = response.data;
                this.updateUIBasedOnPermissions();
            }
        } catch (error) {
            console.error('加载用户权限失败:', error);
        }
    },
    
    // 根据权限更新UI
    updateUIBasedOnPermissions: function() {
        if (!this.state.permissions) return;
        
        // 隐藏无权限的元素
        $('[data-permission]').each((index, element) => {
            const permission = $(element).data('permission');
            if (!this.state.permissions[permission]) {
                $(element).hide();
            }
        });
    },
    
    // 加载通知
    loadNotifications: async function() {
        try {
            const response = await this.api.get('/notifications');
            if (response.success) {
                this.state.notifications = response.data;
                this.updateNotificationUI();
            }
        } catch (error) {
            console.error('加载通知失败:', error);
        }
    },
    
    // 更新通知UI
    updateNotificationUI: function() {
        const count = this.state.notifications.filter(n => !n.read).length;
        const $badge = $('#notificationCount');
        const $list = $('#notificationList');
        const $noNotifications = $('#noNotifications');
        
        if (count > 0) {
            $badge.text(count).show();
            $noNotifications.hide();
        } else {
            $badge.hide();
            $noNotifications.show();
        }
        
        // 更新通知列表
        $list.empty();
        this.state.notifications.slice(0, 5).forEach(notification => {
            const $item = $(`
                <li class="dropdown-item notification-item ${notification.read ? '' : 'fw-bold'}">
                    <div class="d-flex align-items-start">
                        <i class="bi bi-${notification.icon || 'bell'} me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-time text-muted small">
                                ${this.utils.formatTime(notification.created_at)}
                            </div>
                        </div>
                    </div>
                </li>
            `);
            $list.append($item);
        });
    },
    
    // 开始定期更新
    startPeriodicUpdates: function() {
        setInterval(() => {
            this.updateSystemStatus();
            this.updateLastUpdateTime();
        }, this.config.refreshInterval);
    },
    
    // 更新系统状态
    updateSystemStatus: async function() {
        try {
            const response = await this.api.get('/health');
            const $status = $('#systemStatus');
            
            if (response.status === 'healthy') {
                $status.removeClass('bg-warning bg-danger').addClass('bg-success')
                    .html('<i class="bi bi-circle-fill me-1"></i>系统正常');
            } else {
                $status.removeClass('bg-success bg-danger').addClass('bg-warning')
                    .html('<i class="bi bi-exclamation-triangle-fill me-1"></i>系统异常');
            }
        } catch (error) {
            const $status = $('#systemStatus');
            $status.removeClass('bg-success bg-warning').addClass('bg-danger')
                .html('<i class="bi bi-x-circle-fill me-1"></i>连接失败');
        }
    },
    
    // 更新最后更新时间
    updateLastUpdateTime: function() {
        $('#lastUpdateTime').text(this.utils.formatTime(new Date()));
    },
    
    // 处理全局搜索
    handleGlobalSearch: function(e) {
        const query = $(e.target).val().trim();
        if (query.length >= 2) {
            LiveCodeApp.performGlobalSearch(query);
        }
    },
    
    // 执行全局搜索
    performGlobalSearch: async function(query) {
        try {
            this.ui.showLoading();
            const response = await this.api.get(`/search?q=${encodeURIComponent(query)}`);
            
            if (response.success) {
                this.displaySearchResults(response.data);
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.ui.showError('搜索失败，请稍后重试');
        } finally {
            this.ui.hideLoading();
        }
    },
    
    // 显示搜索结果
    displaySearchResults: function(results) {
        // 实现搜索结果显示逻辑
        console.log('搜索结果:', results);
    },
    
    // 处理确认模态框
    handleConfirmModal: function(e) {
        const button = $(e.relatedTarget);
        const message = button.data('message') || '确定要执行此操作吗？';
        const action = button.data('action');
        
        $('#confirmMessage').text(message);
        $('#confirmButton').data('action', action);
    },
    
    // 处理确认操作
    handleConfirmAction: function(e) {
        const action = $(e.target).data('action');
        if (action && typeof window[action] === 'function') {
            window[action]();
        }
        $('#confirmModal').modal('hide');
    },
    
    // 切换侧边栏
    toggleSidebar: function() {
        $('.sidebar').toggleClass('show');
    },
    
    // 处理AJAX表单
    handleAjaxForm: function(e) {
        e.preventDefault();
        const $form = $(this);
        const url = $form.attr('action');
        const method = $form.attr('method') || 'POST';
        const data = new FormData(this);
        
        LiveCodeApp.api.request(url, {
            method: method,
            body: data
        }).then(response => {
            if (response.success) {
                LiveCodeApp.ui.showSuccess(response.message || '操作成功');
                if ($form.data('redirect')) {
                    window.location.href = $form.data('redirect');
                } else if ($form.data('reload')) {
                    window.location.reload();
                }
            } else {
                LiveCodeApp.ui.showError(response.message || '操作失败');
            }
        }).catch(error => {
            console.error('表单提交失败:', error);
            LiveCodeApp.ui.showError('操作失败，请稍后重试');
        });
    },
    
    // 处理批量全选
    handleBatchSelectAll: function(e) {
        const checked = $(this).prop('checked');
        $('.batch-select-item').prop('checked', checked);
        LiveCodeApp.updateBatchActions();
    },
    
    // 处理批量选择项
    handleBatchSelectItem: function(e) {
        LiveCodeApp.updateBatchActions();
        
        const totalItems = $('.batch-select-item').length;
        const checkedItems = $('.batch-select-item:checked').length;
        
        $('.batch-select-all').prop('checked', checkedItems === totalItems);
        $('.batch-select-all').prop('indeterminate', checkedItems > 0 && checkedItems < totalItems);
    },
    
    // 更新批量操作
    updateBatchActions: function() {
        const checkedItems = $('.batch-select-item:checked').length;
        const $batchActions = $('.batch-actions');
        const $selectionInfo = $('.batch-selection-info');
        
        if (checkedItems > 0) {
            $batchActions.removeClass('d-none');
            $selectionInfo.text(`已选择 ${checkedItems} 项`);
        } else {
            $batchActions.addClass('d-none');
        }
    },
    
    // 页面卸载前确认
    handleBeforeUnload: function(e) {
        if (LiveCodeApp.state.hasUnsavedChanges) {
            e.preventDefault();
            e.returnValue = '';
            return '';
        }
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    LiveCodeApp.init();
});

// ===== 工具函数 =====
LiveCodeApp.utils = {
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化时间
    formatTime: function(date, format = 'relative') {
        if (!date) return '';

        const d = new Date(date);
        const now = new Date();
        const diff = now - d;

        if (format === 'relative') {
            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
            return d.toLocaleDateString('zh-CN');
        }

        if (format === 'datetime') {
            return d.toLocaleString('zh-CN');
        }

        if (format === 'date') {
            return d.toLocaleDateString('zh-CN');
        }

        if (format === 'time') {
            return d.toLocaleTimeString('zh-CN');
        }

        return d.toLocaleString('zh-CN');
    },

    // 格式化数字
    formatNumber: function(num, decimals = 0) {
        if (num === null || num === undefined) return '0';

        if (num >= 1000000) {
            return (num / 1000000).toFixed(decimals) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(decimals) + 'K';
        }
        return num.toLocaleString('zh-CN');
    },

    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 生成随机ID
    generateId: function(length = 8) {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text).then(() => {
                LiveCodeApp.ui.showSuccess('已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyToClipboard(text);
            });
        } else {
            this.fallbackCopyToClipboard(text);
        }
    },

    // 备用复制方法
    fallbackCopyToClipboard: function(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            LiveCodeApp.ui.showSuccess('已复制到剪贴板');
        } catch (err) {
            console.error('复制失败:', err);
            LiveCodeApp.ui.showError('复制失败，请手动复制');
        }

        document.body.removeChild(textArea);
    },

    // 验证邮箱
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // 验证URL
    validateUrl: function(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    // 获取URL参数
    getUrlParams: function() {
        const params = {};
        const urlParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlParams) {
            params[key] = value;
        }
        return params;
    },

    // 设置URL参数
    setUrlParams: function(params, replace = false) {
        const url = new URL(window.location);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });

        if (replace) {
            window.history.replaceState({}, '', url);
        } else {
            window.history.pushState({}, '', url);
        }
    },

    // 转义HTML
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // 截断文本
    truncateText: function(text, length = 50, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length) + suffix;
    }
};

// ===== API调用 =====
LiveCodeApp.api = {
    // 基础请求方法
    request: async function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include'
        };

        // 如果是FormData，删除Content-Type让浏览器自动设置
        if (options.body instanceof FormData) {
            delete defaultOptions.headers['Content-Type'];
        }

        const config = { ...defaultOptions, ...options };

        // 添加API基础URL
        if (!url.startsWith('http')) {
            url = LiveCodeApp.config.apiBaseUrl + url;
        }

        try {
            const response = await fetch(url, config);

            // 处理认证失败
            if (response.status === 401) {
                window.location.href = '/auth/login';
                return;
            }

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },

    // GET请求
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    },

    // POST请求
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    // PUT请求
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    // DELETE请求
    delete: function(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    },

    // 上传文件
    upload: function(url, formData) {
        return this.request(url, {
            method: 'POST',
            body: formData
        });
    }
};

// ===== UI组件 =====
LiveCodeApp.ui = {
    // 显示加载状态
    showLoading: function(message = '加载中...') {
        const $overlay = $('#loadingOverlay');
        $overlay.find('.loading-spinner div:last-child').text(message);
        $overlay.removeClass('d-none');
        LiveCodeApp.state.isLoading = true;
    },

    // 隐藏加载状态
    hideLoading: function() {
        $('#loadingOverlay').addClass('d-none');
        LiveCodeApp.state.isLoading = false;
    },

    // 显示成功消息
    showSuccess: function(message, duration = 3000) {
        this.showToast(message, 'success', duration);
    },

    // 显示错误消息
    showError: function(message, duration = 5000) {
        this.showToast(message, 'danger', duration);
    },

    // 显示警告消息
    showWarning: function(message, duration = 4000) {
        this.showToast(message, 'warning', duration);
    },

    // 显示信息消息
    showInfo: function(message, duration = 3000) {
        this.showToast(message, 'info', duration);
    },

    // 显示Toast消息
    showToast: function(message, type = 'info', duration = 3000) {
        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: 'check-circle-fill',
            danger: 'exclamation-triangle-fill',
            warning: 'exclamation-triangle-fill',
            info: 'info-circle-fill'
        };

        const toast = $(`
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-${iconMap[type]} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        // 添加到页面
        if (!$('#toastContainer').length) {
            $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
        }

        $('#toastContainer').append(toast);

        // 显示Toast
        const bsToast = new bootstrap.Toast(toast[0], {
            autohide: true,
            delay: duration
        });
        bsToast.show();

        // 自动移除
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    },

    // 确认对话框
    confirm: function(message, title = '确认操作') {
        return new Promise((resolve) => {
            const modalId = 'confirmModal-' + Date.now();
            const modal = $(`
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(modal);

            const bsModal = new bootstrap.Modal(modal[0]);
            bsModal.show();

            modal.find('.confirm-btn').on('click', function() {
                resolve(true);
                bsModal.hide();
            });

            modal.on('hidden.bs.modal', function() {
                resolve(false);
                $(this).remove();
            });
        });
    },

    // 输入对话框
    prompt: function(message, defaultValue = '', title = '输入') {
        return new Promise((resolve) => {
            const modalId = 'promptModal-' + Date.now();
            const modal = $(`
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                                <input type="text" class="form-control" value="${defaultValue}" id="promptInput">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(modal);

            const bsModal = new bootstrap.Modal(modal[0]);
            bsModal.show();

            const $input = modal.find('#promptInput');
            $input.focus().select();

            modal.find('.confirm-btn').on('click', function() {
                resolve($input.val());
                bsModal.hide();
            });

            $input.on('keypress', function(e) {
                if (e.which === 13) {
                    resolve($input.val());
                    bsModal.hide();
                }
            });

            modal.on('hidden.bs.modal', function() {
                resolve(null);
                $(this).remove();
            });
        });
    },

    // 创建数据表格
    createDataTable: function(selector, options = {}) {
        const defaultOptions = {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json'
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        };

        return $(selector).DataTable({ ...defaultOptions, ...options });
    },

    // 更新页面标题
    updatePageTitle: function(title) {
        document.title = title + ' - 活码管理平台';
    },

    // 设置页面加载状态
    setPageLoading: function(loading) {
        if (loading) {
            $('body').addClass('loading');
        } else {
            $('body').removeClass('loading');
        }
    }
};

// ===== 全局函数 =====

// 登出函数
function logout() {
    LiveCodeApp.ui.confirm('确定要退出登录吗？').then(confirmed => {
        if (confirmed) {
            LiveCodeApp.api.post('/auth/logout').then(() => {
                window.location.href = '/auth/login';
            }).catch(error => {
                console.error('登出失败:', error);
                // 即使登出失败也跳转到登录页
                window.location.href = '/auth/login';
            });
        }
    });
}

// 创建活码
function createLiveCode() {
    window.location.href = '/livecode/create';
}

// 导入数据
function importData() {
    window.location.href = '/admin/import';
}

// 显示帮助
function showHelp() {
    window.open('/help', '_blank');
}

// 复制活码链接
function copyLiveCodeUrl(code) {
    const url = window.location.origin + '/' + code;
    LiveCodeApp.utils.copyToClipboard(url);
}

// 预览活码
function previewLiveCode(code) {
    window.open('/' + code + '/preview', '_blank');
}

// 删除活码
function deleteLiveCode(id, title) {
    LiveCodeApp.ui.confirm(`确定要删除活码"${title}"吗？此操作不可撤销。`, '删除确认').then(confirmed => {
        if (confirmed) {
            LiveCodeApp.api.delete(`/livecodes/${id}`).then(response => {
                if (response.success) {
                    LiveCodeApp.ui.showSuccess('活码删除成功');
                    // 刷新页面或移除行
                    window.location.reload();
                } else {
                    LiveCodeApp.ui.showError(response.message || '删除失败');
                }
            }).catch(error => {
                console.error('删除活码失败:', error);
                LiveCodeApp.ui.showError('删除失败，请稍后重试');
            });
        }
    });
}

// 切换活码状态
function toggleLiveCodeStatus(id, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? '启用' : '停用';

    LiveCodeApp.ui.confirm(`确定要${action}此活码吗？`).then(confirmed => {
        if (confirmed) {
            LiveCodeApp.api.put(`/livecodes/${id}`, { status: newStatus }).then(response => {
                if (response.success) {
                    LiveCodeApp.ui.showSuccess(`活码${action}成功`);
                    window.location.reload();
                } else {
                    LiveCodeApp.ui.showError(response.message || `${action}失败`);
                }
            }).catch(error => {
                console.error(`${action}活码失败:`, error);
                LiveCodeApp.ui.showError(`${action}失败，请稍后重试`);
            });
        }
    });
}
