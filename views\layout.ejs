<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || '活码管理平台' %></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
    
    <% if (typeof additionalCSS !== 'undefined') { %>
        <% additionalCSS.forEach(function(css) { %>
            <link href="<%= css %>" rel="stylesheet">
        <% }); %>
    <% } %>
</head>
<body class="<%= bodyClass || '' %>">
    <!-- Navigation -->
    <% if (typeof showNavigation === 'undefined' || showNavigation) { %>
        <%- include('partials/navbar') %>
    <% } %>

    <!-- Main Content -->
    <div class="<%= typeof user !== 'undefined' ? 'container-fluid' : 'container' %>">
        <% if (typeof user !== 'undefined') { %>
            <!-- Sidebar for authenticated users -->
            <div class="row">
                <div class="col-md-3 col-lg-2 sidebar">
                    <%- include('partials/sidebar') %>
                </div>
                <div class="col-md-9 col-lg-10 main-content">
                    <!-- Breadcrumb -->
                    <% if (typeof breadcrumb !== 'undefined') { %>
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb">
                                <% breadcrumb.forEach(function(item, index) { %>
                                    <% if (index === breadcrumb.length - 1) { %>
                                        <li class="breadcrumb-item active" aria-current="page"><%= item.name %></li>
                                    <% } else { %>
                                        <li class="breadcrumb-item">
                                            <% if (item.url) { %>
                                                <a href="<%= item.url %>"><%= item.name %></a>
                                            <% } else { %>
                                                <%= item.name %>
                                            <% } %>
                                        </li>
                                    <% } %>
                                <% }); %>
                            </ol>
                        </nav>
                    <% } %>

                    <!-- Page Header -->
                    <% if (typeof pageHeader !== 'undefined') { %>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h1 class="h3 mb-0"><%= pageHeader.title %></h1>
                                <% if (pageHeader.subtitle) { %>
                                    <p class="text-muted mb-0"><%= pageHeader.subtitle %></p>
                                <% } %>
                            </div>
                            <% if (pageHeader.actions) { %>
                                <div class="btn-group">
                                    <% pageHeader.actions.forEach(function(action) { %>
                                        <a href="<%= action.url %>" class="btn <%= action.class || 'btn-primary' %>">
                                            <% if (action.icon) { %>
                                                <i class="bi bi-<%= action.icon %>"></i>
                                            <% } %>
                                            <%= action.text %>
                                        </a>
                                    <% }); %>
                                </div>
                            <% } %>
                        </div>
                    <% } %>

                    <!-- Flash Messages -->
                    <%- include('partials/flash-messages') %>

                    <!-- Main Content Area -->
                    <%- body %>
                </div>
            </div>
        <% } else { %>
            <!-- Full width for non-authenticated users -->
            <%- body %>
        <% } %>
    </div>

    <!-- Footer -->
    <% if (typeof showFooter === 'undefined' || showFooter) { %>
        <%- include('partials/footer') %>
    <% } %>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">加载中...</div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage">确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmButton">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/app.js"></script>
    
    <% if (typeof additionalJS !== 'undefined') { %>
        <% additionalJS.forEach(function(js) { %>
            <script src="<%= js %>"></script>
        <% }); %>
    <% } %>

    <% if (typeof inlineJS !== 'undefined') { %>
        <script>
            <%- inlineJS %>
        </script>
    <% } %>
</body>
</html>
